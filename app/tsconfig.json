{"compilerOptions": {"baseUrl": "./", "paths": {"@src/*": ["./src/*"], "@assets/*": ["./src/assets/*"], "@pages/*": ["./src/pages/*"], "@components/*": ["./src/components/*"], "@apps/*": ["./src/pages/pageHome/components/desktop/apps/*"]}, "target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "sourceMap": true, "types": ["vitest/globals"], "noUnusedParameters": true, "noUnusedLocals": true, "composite": true}, "ts-node": {"esm": true}, "include": ["src/**/*", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.d.ts", "**/*.json"], "exclude": ["node_modules", "vite.config.ts", "tsconfig.json"]}